services: 
  redis:
    image: ${CONTAINER_REGISTRY_BASE}/redis:latest
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_redis
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_redis
    restart: always
    ports:
      - "${REDIS_EXPOSED_PORT}:${REDIS_PORT}"
    networks:
      - custom_network
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    env_file:
      - .env
    volumes:
      - ./localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "redis-cli", "-h", "127.0.0.1", "-p", "6379", "ping"]
      interval: 30s      # Check every 30 seconds
      timeout: 10s       # Wait for 10 seconds for a response 
      retries: 3         # Retry 3 times before considering the container is unhealthy
      start_period: 10s  # Allow container 10 seconds to start before health checks begin