import asyncio
import pandas as pd
from redis import asyncio as aioredis
from exchange.helpers.configstore import REDIS_HOST, REDIS_PORT
from exchange.helpers.redis_data_handler_components.datasnap_client import DataSnapClient
from core.helpers.utils import get_local_timestamp
import balte.balte_config
from toti.toti_utility import parse_contract, contract_name_to_balte_id


class RedisDataClient:
    """
    A Redis client for handling real-time tick data storage and retrieval.

    This class provides functionality to connect to Redis, store tick data
    with balte_id to LTP (Last Traded Price) mappings, and continuously
    fetch and store market data.

    Attributes:
        redis_host (str): Redis server hostname from configuration
        redis_port (int): Redis server port from configuration
        redis_pool: Redis connection pool for async operations
        datasnap_client (DataSnapClient): Client for fetching data from datasnap service
        is_market_closed (asyncio.Future): Flag to indicate if market is closed
        market_closing_time (pd.Timestamp): Timestamp of market closing time
    """

    def __init__(self):
        """
        Initialize the Redis data client.

        Sets up Redis connection parameters from configuration and
        initializes the Redis connection pool.
        """
        self.redis_host = REDIS_HOST
        self.redis_port = REDIS_PORT
        self.redis_db = ""
        self.redis_pool = self.create_redis_pool()
        self.datasnap_client = DataSnapClient()
        self.is_market_closed: asyncio.Future = asyncio.Future()  # type: ignore
        self.market_closing_time: pd.Timestamp = (
            get_local_timestamp()
            .normalize()
            .replace(
                hour=balte.balte_config.MARKET_CLOSE_HOUR,
                minute=balte.balte_config.MARKET_CLOSE_MINUTE,
            )
        )

    async def create_redis_pool(self):
        """
        Create and initialize Redis client pool.

        Establishes an asynchronous connection to Redis server using the
        configured host and port settings.

        Returns:
            Redis: Async Redis client instance
        """
        self.redis_pool = await aioredis.Redis(host=self.redis_host, port=self.redis_port, db=self.redis_db)
        
    def get_balte_id(self, contract: str) -> int:
        """
        Get the balte_id for a given contract.

        Args:
            contract (str): The contract symbol

        Returns:
            int: The balte_id associated with the contract
        """
        balte_id = None
        symbol, expiry, option_type, strike = parse_contract(contract)
        if option_type != "":
            balte_id = contract_name_to_balte_id(universe="optcom", contract_name=contract)
        elif expiry 
        else:
            balte_id = contract_name_to_balte_id(universe="futcom", contract_name=contract)
        return balte_id
        

    async def store_balte_id_to_ltp_in_redis(self, tick_data):
        """
        Store balte_id to Last Traded Price (LTP) mappings in Redis.

        Processes tick data and stores each contract's LTP in Redis using
        the balte_id as the key. Operations are performed concurrently
        for better performance.

        Args:
            tick_data (list): List of tick data strings in format "id|field1|ltp|..."
                            where contract is first element and LTP is third element

        Raises:
            Exception: If Redis pool is not initialized

        Note:
            Assumes tick data format: "contract|field1|ltp|other_fields"
            All operations are executed concurrently using asyncio.gather()
        """
        if self.redis_pool is None:
            raise Exception("Redis pool is not initialized")

        coroutines = []
        for tick in tick_data:
            tick = str(tick)
            contract = tick.split("|")[0]
            balte_id = self.get_balte_id(contract=contract)
            ltp = tick.split("|")[2]

            coroutines.append(self.redis_pool.set(balte_id, ltp))

        await asyncio.gather(*coroutines)
        
    async def market_termination_checker(self) -> None:
        """Calculates the time remaining in market closing and sleeps for that time.

        This function calculates the time remaining in market closing and sleeps for that time.
        Post that, it sets a flag which signals other async processes to stop as well.
        """
        seconds_until_market_termination: int = max(
            0, (self.market_closing_time - get_local_timestamp()).seconds
        )
        await asyncio.sleep(seconds_until_market_termination)
        self.is_market_closed.set_result(True)

    async def fetch_and_store_data(self):
        """
        Continuously fetch market data and store it in Redis.

        Runs an infinite loop that:
        1. Gets the latest timestamp from the data source
        2. Fetches tick data for that timestamp
        3. Stores the tick data in Redis as ID-LTP mappings

        Args:
            get_latest_second (callable): Function that returns an object with
                                        a 'timestamp' attribute containing the latest second
            get_tick_data (callable): Function that takes a timestamp and returns
                                    an object with a 'message' attribute containing tick data

        Note:
            This method runs indefinitely until an exception occurs.
            Errors are caught and printed but the loop continues.
        """
        while not self.is_market_closed.done():
            try:
                latest_timestamp = (self.datasnap_client.get_latest_second(segment="MCX")).timestamp
                tick_data = self.datasnap_client.get_tick_data(segment="MCX", timestamp=latest_timestamp)
                await self.store_balte_id_to_ltp_in_redis(tick_data.message)
            except Exception as e:
                print(f"Error occurred: {e}")
                
    async def redis_data_client_runner(self):
        """
        Run the core event loop for the Redis data client.

        Starts the continuous data fetching and storage process by running
        the fetch_and_store_data method in the asyncio event loop.

        Note:
            This method blocks until the fetch_and_store_data loop is interrupted.
            Uses the current event loop to run the async operations.
        """
        await asyncio.gather(self.fetch_and_store_data())

    def run(self):
        """
        Starts the Redis data client.

        This method initializes the asyncio event loop and runs the
        redis_data_client_runner coroutine within it. It blocks until
        the event loop is stopped.
        """
        asyncio.run(self.redis_data_client_runner())
