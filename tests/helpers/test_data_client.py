from exchange.helpers.one_second_exit_application_components.data_client import (
    OneSecondExitApplicationDataClient,
)
import pytest
import logging
import fakeredis.aioredis as fkaioredis


@pytest.fixture
def custom_redis_client(mocker):
    """Fixture to provide a RedisClient instance backed by a fake Redis server.

    This fixture uses fakeredis.aioredis to mock Redis functionality,
    enabling tests to run without requiring a live Redis server. The mock
    ensures that any calls to `Redis.from_url` in the RedisClient
    implementation are redirected to the fake Redis instance.

    Args:
        mocker: A pytest-mock object used for patching the Redis client.

    Returns:
        RedisClient: A Redis client instance configured to use the fake Redis server.
    """
    fake_redis = fkaioredis.FakeRedis(decode_responses=True)
    mocker.patch(
        "exchange.helpers.one_second_exit_application_components.data_client.aioredis.Redis.from_url",
        return_value=fake_redis,
    )
    return OneSecondExitApplicationDataClient(logger=logging.getLogger())


@pytest.mark.asyncio
async def test_connect_to_redis(custom_redis_client):
    """Test Redis connection is established."""
    assert custom_redis_client.redis_client is None
    await custom_redis_client.connect_to_redis()
    assert custom_redis_client.redis_client is not None


@pytest.mark.asyncio
async def test_disconnect_from_redis(custom_redis_client):
    """Test Redis disconnection."""
    await custom_redis_client.connect_to_redis()
    assert custom_redis_client.redis_client is not None
    await custom_redis_client.disconnect_from_redis()
    assert custom_redis_client.redis_client is None


@pytest.mark.asyncio
async def test_pipelined_read_from_redis(custom_redis_client):
    """Test fetching multiple keys from Redis using pipeline."""
    await custom_redis_client.connect_to_redis()

    # Add mock data to Redis
    await custom_redis_client.redis_client.hset(
        "1001", mapping={"last_traded_price": "200.5"}
    )
    await custom_redis_client.redis_client.hset(
        "1002", mapping={"last_traded_price": "150.3"}
    )

    # Fetch data using the method
    keys = [1001, 1002]
    result = await custom_redis_client.pipelined_read_from_redis(keys)

    decoded_result = {
        key: {
            k.decode() if isinstance(k, bytes) else k: v.decode()
            if isinstance(v, bytes)
            else v
            for k, v in value.items()
        }
        for key, value in result.items()
    }

    assert decoded_result == {
        1001: {"last_traded_price": "200.5"},
        1002: {"last_traded_price": "150.3"},
    }


@pytest.mark.asyncio
async def test_fetch_data(custom_redis_client):
    """Test fetching latest traded price data for active `balte_id`s."""
    await custom_redis_client.connect_to_redis()

    # Add mock data
    await custom_redis_client.redis_client.hset(
        "1001", mapping={"last_traded_price": "200.5"}
    )
    await custom_redis_client.redis_client.hset(
        "1002", mapping={"last_traded_price": "150.3"}
    )

    # Fetch data
    active_balte_ids = [1001, 1002, 1003]  # 1003 is missing in Redis
    result = await custom_redis_client.fetch_data(active_balte_ids)

    assert result == {1001: 200.5, 1002: 150.3}  # 1003 should not be included


@pytest.mark.asyncio
async def test_fetch_data_with_exception(custom_redis_client, mocker):
    """Test fetch_data handles exceptions gracefully."""
    mocker.patch.object(
        custom_redis_client,
        "pipelined_read_from_redis",
        side_effect=Exception("Redis error"),
    )

    result = await custom_redis_client.fetch_data([1001, 1002])
    assert result == {}  # Should return an empty dict on failure
    assert custom_redis_client.redis_client is None
